# Learneezi - Learning Made Easy

A modern learning platform built with Next.js, TypeScript, and Material UI (MUI).

## Project Structure

```
├── public/             # Static assets
├── src/
│   ├── app/            # Next.js App Router pages
│   ├── components/     # Reusable UI components
│   │   ├── common/     # Common UI components
│   │   └── layout/     # Layout components
│   ├── context/        # React Context providers
│   ├── hooks/          # Custom React hooks
│   ├── lib/            # Third-party library configurations
│   ├── theme/          # MUI theme configuration
│   ├── types/          # TypeScript type definitions
│   └── utils/          # Utility functions
```

## Features

- **Modern Stack**: Built with Next.js, TypeScript, and Material UI for a modern, responsive user experience.
- **Scalable Architecture**: Organized project structure with components, hooks, utils, and types for maintainability.
- **Customizable Theme**: Fully customizable theme with light and dark mode support using MUI's theming system.
- **Type Safety**: Comprehensive TypeScript integration for better developer experience and code quality.

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or yarn

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/learneezi.git
cd learneezi

# Install dependencies
npm install
# or
yarn install

# Start the development server
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the application for production
- `npm start` - Start the production server
- `npm run lint` - Run ESLint to check code quality

## Learn More

To learn more about the technologies used in this project, check out the following resources:

- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Material UI Documentation](https://mui.com/material-ui/getting-started/)
