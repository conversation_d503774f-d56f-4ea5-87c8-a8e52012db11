// Environment variable configuration

// Define the environment variables we need for the application
interface Env {
  NEXT_PUBLIC_API_URL: string;
  NEXT_PUBLIC_APP_ENV: 'development' | 'staging' | 'production';
  NEXT_PUBLIC_APP_VERSION: string;
}

// Get environment variables with type safety
export const env: Env = {
  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  NEXT_PUBLIC_APP_ENV: (process.env.NEXT_PUBLIC_APP_ENV as Env['NEXT_PUBLIC_APP_ENV']) || 'development',
  NEXT_PUBLIC_APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '0.1.0',
};

// Helper function to check if we're in development mode
export const isDev = env.NEXT_PUBLIC_APP_ENV === 'development';

// Helper function to check if we're in production mode
export const isProd = env.NEXT_PUBLIC_APP_ENV === 'production';

// Helper function to check if we're in staging mode
export const isStaging = env.NEXT_PUBLIC_APP_ENV === 'staging';