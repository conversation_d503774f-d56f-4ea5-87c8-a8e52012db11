'use client';

import { useRef, useEffect, useState } from 'react';
import Grid from '@mui/joy/Grid';
import Typography from '@mui/joy/Typography';
import Box from '@mui/joy/Box';
import Card from '@/components/common/Card';
import Button from '@/components/common/Button';
import Image from 'next/image';
import { Parallax, ParallaxBanner, ParallaxBannerLayer, useParallax } from 'react-scroll-parallax';
import dynamic from 'next/dynamic';

// Dynamically import components with no SSR to avoid hydration issues
const NightSky = dynamic(() => import('@/components/common/NightSky'), { ssr: false });
const Moon = dynamic(() => import('@/components/common/Moon'), { ssr: false });

export default function Home() {
  // No need for refs with react-scroll-parallax library
  
  return (
    <Box sx={{ py: 4 }}>
      {/* Hero Section with Parallax */}
      <ParallaxBanner
        style={{ 
          height: '600px',
          marginBottom: '3rem',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Night Sky Background with Stars */}
        <ParallaxBannerLayer speed={-10}>
          <Box sx={{
            height: '100%',
            width: '100%',
            position: 'absolute',
            overflow: 'hidden'
          }}>
            <Box 
              component="div" 
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: 'linear-gradient(to bottom, #0f2027, #203a43, #2c5364)',
                zIndex: 0
              }}
            />
            {/* Night Sky component with falling stars */}
            <Box sx={{ position: 'relative', height: '100%', width: '100%' }}>
              <NightSky starCount={150} />
            </Box>
          </Box>
        </ParallaxBannerLayer>
        
        {/* Moving Moon */}
        <ParallaxBannerLayer speed={-15}>
          <Moon 
            translateX={['-50px', '50px']} 
            translateY={['0px', '30px']} 
            rotateZ={[0, 15]} 
            scale={[1, 1.2]} 
          />
        </ParallaxBannerLayer>
        
        {/* Content Layer */}
        <ParallaxBannerLayer speed={-5}>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            width: '100%',
            textAlign: 'center',
            padding: '2rem',
            position: 'relative',
            zIndex: 2
          }}>
            <Typography 
              level="h1" 
              sx={{ 
                mb: 2, 
                color: 'white',
                textShadow: '0 0 10px rgba(255,255,255,0.5)'
              }}
            >
              Welcome to Learneezi
            </Typography>
            <Typography 
              level="body-lg" 
              sx={{ 
                mb: 4, 
                maxWidth: '800px', 
                mx: 'auto',
                color: 'white',
                textShadow: '0 0 5px rgba(255,255,255,0.3)'
              }}
            >
              A modern learning platform built with Next.js, TypeScript, and MUI
            </Typography>
            <Button 
              size="lg"
              sx={{
                background: 'rgba(255,255,255,0.2)',
                backdropFilter: 'blur(5px)',
                '&:hover': {
                  background: 'rgba(255,255,255,0.3)'
                }
              }}
            >
              Get Started
            </Button>
          </Box>
        </ParallaxBannerLayer>
      </ParallaxBanner>

      {/* Features Section with Parallax Cards */}
      <Typography level="h2" sx={{ textAlign: 'center', mb: 4, mt: 6 }}>
        Our Features
      </Typography>
      <Grid container spacing={3} sx={{ mb: 6 }}>
        <Grid xs={12} md={4}>
          <Parallax speed={-5} translateX={['-30px', '0px']} opacity={[0.5, 1]} easing="easeInQuad">
            <Card 
              title="Modern Stack" 
              sx={{ 
                height: '100%',
                transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: 'md',
                }
              }}
              footer={
                <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button variant="plain">Learn More</Button>
                </Box>
              }
            >
              <Typography>
                Built with Next.js, TypeScript, and Material UI for a modern, responsive user experience.
              </Typography>
            </Card>
          </Parallax>
        </Grid>
        <Grid xs={12} md={4}>
          <Parallax speed={-10} translateY={['30px', '0px']} opacity={[0.5, 1]} easing="easeInQuad" startScroll={100}>
            <Card 
              title="Scalable Architecture" 
              sx={{ 
                height: '100%',
                transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: 'md',
                }
              }}
              footer={
                <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button variant="plain">Learn More</Button>
                </Box>
              }
            >
              <Typography>
                Organized project structure with components, hooks, utils, and types for maintainability.
              </Typography>
            </Card>
          </Parallax>
        </Grid>
        <Grid xs={12} md={4}>
          <Parallax speed={-5} translateX={['30px', '0px']} opacity={[0.5, 1]} easing="easeInQuad" startScroll={200}>
            <Card 
              title="Customizable Theme" 
              sx={{ 
                height: '100%',
                transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: 'md',
                }
              }}
              footer={
                <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button variant="plain">Learn More</Button>
                </Box>
              }
            >
              <Typography>
                Fully customizable theme with light and dark mode support using MUI's theming system.
              </Typography>
            </Card>
          </Parallax>
        </Grid>
        
        {/* Additional Cards */}
        <Grid xs={12} md={6} sx={{ mt: 4 }}>
          <Parallax speed={-8} translateY={['50px', '-20px']} opacity={[0.3, 1]} easing="easeOutQuad" startScroll={300}>
            <Card 
              title="Interactive Learning" 
              sx={{ 
                height: '100%',
                transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: 'md',
                }
              }}
              footer={
                <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button variant="plain">Learn More</Button>
                </Box>
              }
            >
              <Typography>
                Engage with interactive content that adapts to your learning style and pace. Our platform offers quizzes, challenges, and hands-on exercises to reinforce your understanding.
              </Typography>
            </Card>
          </Parallax>
        </Grid>
        <Grid xs={12} md={6} sx={{ mt: 4 }}>
          <Parallax speed={-12} translateY={['70px', '-30px']} opacity={[0.3, 1]} easing="easeOutQuad" startScroll={350}>
            <Card 
              title="Progress Tracking" 
              sx={{ 
                height: '100%',
                transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: 'md',
                }
              }}
              footer={
                <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button variant="plain">Learn More</Button>
                </Box>
              }
            >
              <Typography>
                Monitor your learning journey with detailed analytics and progress reports. Set goals, track achievements, and visualize your growth over time.
              </Typography>
            </Card>
          </Parallax>
        </Grid>
        
        <Grid xs={12} md={4} sx={{ mt: 4 }}>
          <Parallax speed={-7} translateX={['-40px', '10px']} rotateZ={[0, 2]} opacity={[0.4, 1]} easing="easeInOutQuad" startScroll={400}>
            <Card 
              title="Community Support" 
              sx={{ 
                height: '100%',
                transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: 'md',
                }
              }}
              footer={
                <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button variant="plain">Learn More</Button>
                </Box>
              }
            >
              <Typography>
                Join a vibrant community of learners and educators. Collaborate on projects, participate in discussions, and share resources.
              </Typography>
            </Card>
          </Parallax>
        </Grid>
        <Grid xs={12} md={4} sx={{ mt: 4 }}>
          <Parallax speed={-9} translateY={['60px', '-15px']} rotateZ={[0, -2]} opacity={[0.4, 1]} easing="easeInOutQuad" startScroll={450}>
            <Card 
              title="Expert Mentorship" 
              sx={{ 
                height: '100%',
                transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: 'md',
                }
              }}
              footer={
                <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button variant="plain">Learn More</Button>
                </Box>
              }
            >
              <Typography>
                Connect with industry experts and experienced mentors who provide guidance, feedback, and insights to accelerate your learning.
              </Typography>
            </Card>
          </Parallax>
        </Grid>
        <Grid xs={12} md={4} sx={{ mt: 4 }}>
          <Parallax speed={-6} translateX={['40px', '-10px']} rotateZ={[0, 1]} opacity={[0.4, 1]} easing="easeInOutQuad" startScroll={500}>
            <Card 
              title="Personalized Learning" 
              sx={{ 
                height: '100%',
                transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: 'md',
                }
              }}
              footer={
                <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button variant="plain">Learn More</Button>
                </Box>
              }
            >
              <Typography>
                Experience tailored learning paths that adapt to your goals, preferences, and pace. Our AI-powered system recommends content based on your progress and interests.
              </Typography>
            </Card>
          </Parallax>
        </Grid>
      </Grid>
    </Box>
  );
}
