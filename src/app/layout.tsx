import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import ThemeProvider from "@/theme/ThemeProvider";
import MainLayout from "@/components/layout/MainLayout";
import ParallaxWrapper from "@/components/layout/ParallaxWrapper";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Learneezi - Learning Made Easy",
  description: "A modern learning platform built with Next.js, TypeScript, and MUI",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable}`}>
        <ThemeProvider>
          <ParallaxWrapper>
            <MainLayout>
              {children}
            </MainLayout>
          </ParallaxWrapper>
        </ThemeProvider>
      </body>
    </html>
  );
}
