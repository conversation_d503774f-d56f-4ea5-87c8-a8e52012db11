// This file contains custom type declarations for the project

// Extend the Window interface
declare global {
  interface Window {
    // Add any custom window properties here
    dataLayer: any[];
  }
}

// Declare module for importing SVGs
declare module '*.svg' {
  import React from 'react';
  const SVG: React.FC<React.SVGProps<SVGSVGElement>>;
  export default SVG;
}

// Declare module for importing CSS modules
declare module '*.module.css' {
  const classes: { readonly [key: string]: string };
  export default classes;
}

// Declare module for importing JSON
declare module '*.json' {
  const value: any;
  export default value;
}

// Ensure this is treated as a module
export {};