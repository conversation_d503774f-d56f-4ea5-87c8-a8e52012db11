// Common type definitions for the application

// User type
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'admin' | 'user' | 'guest';
  createdAt: Date;
  updatedAt: Date;
}

// API response type
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: Record<string, string[]>;
}

// Pagination type
export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    currentPage: number;
    lastPage: number;
    perPage: number;
    total: number;
  };
}

// Theme mode type
export type ThemeMode = 'light' | 'dark' | 'system';

// Navigation item type
export interface NavigationItem {
  title: string;
  path: string;
  icon?: React.ReactNode;
  children?: NavigationItem[];
}