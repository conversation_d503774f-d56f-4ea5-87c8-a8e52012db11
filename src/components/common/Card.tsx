'use client';

import { ReactNode } from 'react';
import JoyCard from '@mui/joy/Card';
import CardContent from '@mui/joy/CardContent';
import CardOverflow from '@mui/joy/CardOverflow';
import Typography from '@mui/joy/Typography';
import { SxProps } from '@mui/joy/styles/types';

interface CardProps {
  title?: string;
  children: ReactNode;
  footer?: ReactNode;
  sx?: SxProps;
}

export default function Card({ title, children, footer, sx }: CardProps) {
  return (
    <JoyCard variant="outlined" sx={{ ...sx }}>
      {title && (
        <Typography level="title-md" sx={{ p: 2 }}>
          {title}
        </Typography>
      )}
      <CardContent>{children}</CardContent>
      {footer && (
        <CardOverflow variant="soft" sx={{ bgcolor: 'background.level1' }}>
          {footer}
        </CardOverflow>
      )}
    </JoyCard>
  );
}