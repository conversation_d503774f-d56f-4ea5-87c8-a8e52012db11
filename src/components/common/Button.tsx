'use client';

import { ReactNode } from 'react';
import JoyButton from '@mui/joy/Button';
import { SxProps } from '@mui/joy/styles/types';

interface ButtonProps {
  children: ReactNode;
  variant?: 'solid' | 'soft' | 'outlined' | 'plain';
  color?: 'primary' | 'neutral' | 'danger' | 'success' | 'warning';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  startDecorator?: ReactNode;
  endDecorator?: ReactNode;
  sx?: SxProps;
}

export default function Button({
  children,
  variant = 'solid',
  color = 'primary',
  size = 'md',
  fullWidth = false,
  disabled = false,
  loading = false,
  onClick,
  startDecorator,
  endDecorator,
  sx,
}: ButtonProps) {
  return (
    <JoyButton
      variant={variant}
      color={color}
      size={size}
      fullWidth={fullWidth}
      disabled={disabled || loading}
      loading={loading}
      onClick={onClick}
      startDecorator={startDecorator}
      endDecorator={endDecorator}
      sx={sx}
    >
      {children}
    </JoyButton>
  );
}