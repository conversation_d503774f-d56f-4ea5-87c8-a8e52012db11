'use client';

import { useParallax } from 'react-scroll-parallax';
import Box from '@mui/joy/Box';

interface MoonProps {
  size?: number;
  color?: string;
  glowColor?: string;
  translateX?: [string, string];
  translateY?: [string, string];
  rotateZ?: [number, number];
  scale?: [number, number];
}

const Moon = ({
  size = 80,
  color = '#FFFDE7',
  glowColor = 'rgba(255, 253, 231, 0.3)',
  translateX = ['-50px', '50px'],
  translateY = ['0px', '0px'],
  rotateZ = [0, 0],
  scale = [1, 1.2]
}: MoonProps) => {
  const { ref } = useParallax<HTMLDivElement>({
    translateX,
    translateY,
    rotateZ,
    scale,
    easing: 'easeInQuad'
  });

  return (
    <Box
      ref={ref}
      sx={{
        position: 'absolute',
        top: '50px',
        right: '100px',
        width: `${size}px`,
        height: `${size}px`,
        borderRadius: '50%',
        backgroundColor: color,
        boxShadow: `0 0 ${size/2}px ${size/4}px ${glowColor}`,
        '&::before': {
          content: '""',
          position: 'absolute',
          top: '10%',
          left: '10%',
          width: '20%',
          height: '20%',
          borderRadius: '50%',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          filter: 'blur(2px)'
        },
        '&::after': {
          content: '""',
          position: 'absolute',
          top: '40%',
          left: '40%',
          width: '15%',
          height: '15%',
          borderRadius: '50%',
          backgroundColor: 'rgba(255, 255, 255, 0.6)',
          filter: 'blur(1px)'
        }
      }}
    />
  );
};

export default Moon;