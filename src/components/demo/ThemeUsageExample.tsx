'use client';

import { useThemeColors } from '@/utils/colors';
import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';
import Card from '@mui/joy/Card';
import Button from '@/components/common/Button';

export default function ThemeUsageExample() {
  const colors = useThemeColors();

  return (
    <Card
      sx={{
        p: 3,
        mb: 4,
        backgroundColor: colors.background.surface,
        borderColor: colors.divider,
      }}
    >
      <Typography
        level="title-lg"
        sx={{
          mb: 2,
          color: colors.text.primary,
        }}
      >
        Using Theme Colors in Components
      </Typography>

      <Typography
        level="body-md"
        sx={{
          mb: 3,
          color: colors.text.secondary,
        }}
      >
        This component demonstrates how to use the theme colors directly in your components.
        The colors automatically adapt to the current theme mode.
      </Typography>

      <Box
        sx={{
          p: 2,
          borderRadius: 1,
          backgroundColor: colors.isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.03)',
          border: '1px dashed',
          borderColor: colors.divider,
        }}
      >
        <Typography level="body-sm" sx={{ mb: 2, fontFamily: 'code' }}>
          {`// Example code
import { useThemeColors } from '@/utils/colors';

function MyComponent() {
  const colors = useThemeColors();
  
  return (
    <Box sx={{ 
      backgroundColor: colors.background.surface,
      color: colors.text.primary 
    }}>
      Content
    </Box>
  );
}`}
        </Typography>
      </Box>

      <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
        <Button color="primary">Primary Button</Button>
        <Button color="neutral" variant="outlined">Neutral Button</Button>
        <Button
          variant="soft"
          sx={{
            backgroundColor: colors.isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
            color: colors.text.primary,
          }}
        >
          Custom Styled
        </Button>
      </Box>
    </Card>
  );
}