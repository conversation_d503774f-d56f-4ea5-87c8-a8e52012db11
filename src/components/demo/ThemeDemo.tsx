'use client';

import { useThemeColors } from '@/utils/colors';
import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';
import Grid from '@mui/joy/Grid';
import Card from '@mui/joy/Card';
import Divider from '@mui/joy/Divider';
import { palette } from '@/theme/theme';

interface ColorSampleProps {
  color: string;
  name: string;
}

function ColorSample({ color, name }: ColorSampleProps) {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
      <Box
        sx={{
          width: 60,
          height: 60,
          borderRadius: '8px',
          backgroundColor: color,
          border: '1px solid',
          borderColor: 'divider',
        }}
      />
      <Typography level="body-sm">{name}</Typography>
    </Box>
  );
}

export default function ThemeDemo() {
  const colors = useThemeColors();

  return (
    <Box sx={{ py: 4 }}>
      <Typography level="h2" sx={{ mb: 3 }}>
        Theme Colors Demo
      </Typography>

      <Grid container spacing={3}>
        {/* Primary Colors */}
        <Grid xs={12}>
          <Card sx={{ p: 3, mb: 3 }}>
            <Typography level="title-lg" sx={{ mb: 2 }}>
              Primary Colors
            </Typography>
            <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
              <ColorSample color={palette.primary[50]} name="50" />
              <ColorSample color={palette.primary[100]} name="100" />
              <ColorSample color={palette.primary[200]} name="200" />
              <ColorSample color={palette.primary[300]} name="300" />
              <ColorSample color={palette.primary[400]} name="400" />
              <ColorSample color={palette.primary[500]} name="500" />
              <ColorSample color={palette.primary[600]} name="600" />
              <ColorSample color={palette.primary[700]} name="700" />
              <ColorSample color={palette.primary[800]} name="800" />
              <ColorSample color={palette.primary[900]} name="900" />
            </Box>
          </Card>
        </Grid>

        {/* Success, Warning, Danger */}
        <Grid xs={12} md={4}>
          <Card sx={{ p: 3, height: '100%' }}>
            <Typography level="title-lg" sx={{ mb: 2 }}>
              Success Colors
            </Typography>
            <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
              <ColorSample color={palette.success[300]} name="300" />
              <ColorSample color={palette.success[500]} name="500" />
              <ColorSample color={palette.success[700]} name="700" />
            </Box>
          </Card>
        </Grid>

        <Grid xs={12} md={4}>
          <Card sx={{ p: 3, height: '100%' }}>
            <Typography level="title-lg" sx={{ mb: 2 }}>
              Warning Colors
            </Typography>
            <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
              <ColorSample color={palette.warning[300]} name="300" />
              <ColorSample color={palette.warning[500]} name="500" />
              <ColorSample color={palette.warning[700]} name="700" />
            </Box>
          </Card>
        </Grid>

        <Grid xs={12} md={4}>
          <Card sx={{ p: 3, height: '100%' }}>
            <Typography level="title-lg" sx={{ mb: 2 }}>
              Danger Colors
            </Typography>
            <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
              <ColorSample color={palette.danger[300]} name="300" />
              <ColorSample color={palette.danger[500]} name="500" />
              <ColorSample color={palette.danger[700]} name="700" />
            </Box>
          </Card>
        </Grid>

        {/* Theme-aware colors */}
        <Grid xs={12}>
          <Card sx={{ p: 3, mt: 3 }}>
            <Typography level="title-lg" sx={{ mb: 2 }}>
              Theme-Aware Colors
            </Typography>
            <Typography level="body-md" sx={{ mb: 2 }}>
              These colors automatically adapt to light/dark mode
            </Typography>
            
            <Grid container spacing={2}>
              <Grid xs={12} md={6}>
                <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                  <Typography level="title-md">Background Colors</Typography>
                  <Box sx={{ display: 'flex', gap: 3, mt: 2, flexWrap: 'wrap' }}>
                    <ColorSample color={colors.background.body} name="Body" />
                    <ColorSample color={colors.background.surface} name="Surface" />
                    <ColorSample color={colors.background.popup} name="Popup" />
                  </Box>
                </Card>
              </Grid>
              
              <Grid xs={12} md={6}>
                <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                  <Typography level="title-md">Text Colors</Typography>
                  <Box sx={{ display: 'flex', gap: 3, mt: 2, flexWrap: 'wrap' }}>
                    <ColorSample color={colors.text.primary} name="Primary" />
                    <ColorSample color={colors.text.secondary} name="Secondary" />
                    <ColorSample color={colors.text.disabled} name="Disabled" />
                  </Box>
                </Card>
              </Grid>
            </Grid>
            
            <Box sx={{ mt: 2 }}>
              <Typography level="title-sm">Current Mode: {colors.isDark ? 'Dark' : 'Light'}</Typography>
              <Typography level="body-sm">Toggle the theme using the button in the header to see these colors change</Typography>
            </Box>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}