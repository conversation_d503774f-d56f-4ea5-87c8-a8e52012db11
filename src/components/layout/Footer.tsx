'use client';

import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';
import Container from '@mui/joy/Container';
import Link from 'next/link';

export default function Footer() {
  return (
    <Box
      component="footer"
      sx={{
        py: 3,
        borderTop: '1px solid',
        borderColor: 'divider',
        position: 'sticky',
        bottom: 0,
        zIndex: 1000,
        backgroundColor: 'background.body', // Ensure the footer has a background
      }}
    >
      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'center', sm: 'flex-start' },
            gap: 2,
          }}
        >
          <Typography level="body-sm">
            © {new Date().getFullYear()} Learneezi. All rights reserved.
          </Typography>
          <Box
            sx={{
              display: 'flex',
              gap: 3,
              justifyContent: 'center',
            }}
          >
            <Link href="/privacy" passHref>
              <Typography level="body-sm" component="span" sx={{ cursor: 'pointer' }}>
                Privacy
              </Typography>
            </Link>
            <Link href="/terms" passHref>
              <Typography level="body-sm" component="span" sx={{ cursor: 'pointer' }}>
                Terms
              </Typography>
            </Link>
            <Link href="/contact" passHref>
              <Typography level="body-sm" component="span" sx={{ cursor: 'pointer' }}>
                Contact
              </Typography>
            </Link>
          </Box>
        </Box>
      </Container>
    </Box>
  );
}