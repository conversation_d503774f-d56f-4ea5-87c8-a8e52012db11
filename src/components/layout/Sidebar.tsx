'use client';

import { useState, useEffect } from 'react';
import Box from '@mui/joy/Box';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import ListItemButton from '@mui/joy/ListItemButton';
import ListItemContent from '@mui/joy/ListItemContent';
import Typography from '@mui/joy/Typography';
import IconButton from '@mui/joy/IconButton';
import Tooltip from '@mui/joy/Tooltip';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

// Icons (SVG for simplicity)
const HomeIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M9 22V12h6v10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const AboutIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 16v-4M12 8h.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const ContactIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M22 6l-10 7L2 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const ThemeIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 16a4 4 0 100-8 4 4 0 000 8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 2v2M12 20v2M4.93 4.93l1.41 1.41M17.66 17.66l1.41 1.41M2 12h2M20 12h2M6.34 17.66l-1.41 1.41M19.07 4.93l-1.41 1.41" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const MenuIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Add a new icon for collapse/expand
const CollapseIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15 18l-6-6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const ExpandIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9 18l6-6-6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

interface SidebarProps {
  mobileOpen: boolean;
  onMobileClose: () => void;
}

export default function Sidebar({ mobileOpen, onMobileClose }: SidebarProps) {
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);
  
  // Reset collapsed state when screen size changes and dispatch custom event
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 600) { // sm breakpoint
        setCollapsed(false);
      }
    };
    
    // Initial setup - check localStorage
    const savedCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (savedCollapsed && window.innerWidth >= 600) {
      setCollapsed(true);
    }
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Dispatch custom event when collapsed state changes
  useEffect(() => {
    // Only dispatch for desktop view
    if (window.innerWidth >= 600) {
      const event = new CustomEvent('sidebar-toggle', { detail: { collapsed } });
      window.dispatchEvent(event);
    }
  }, [collapsed]);

  const navItems = [
    { text: 'Home', href: '/', icon: <HomeIcon /> },
    { text: 'About', href: '/about', icon: <AboutIcon /> },
    { text: 'Contact', href: '/contact', icon: <ContactIcon /> },
    { text: 'Theme Demo', href: '/theme-demo', icon: <ThemeIcon /> },
  ];

  const isActive = (href: string) => pathname === href;

  const sidebarContent = (
    <Box sx={{ p: collapsed ? 1 : 2 }}>
      <Box sx={{ 
        mb: collapsed ? 2 : 4, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        flexDirection: collapsed ? 'column' : 'row',
        gap: collapsed ? 2 : 0
      }}>
        {!collapsed && (
          <Typography level="h4" sx={{ fontWeight: 'bold' }}>
            Menu
          </Typography>
        )}
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Mobile close button */}
          <Box sx={{ display: { sm: 'none' } }}>
            <IconButton onClick={onMobileClose}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </IconButton>
          </Box>
          
          {/* Collapse/Expand button - only visible on desktop */}
          <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
            <Tooltip title={collapsed ? "Expand" : "Collapse"} placement="right">
              <IconButton 
                onClick={() => setCollapsed(!collapsed)}
                variant="outlined"
                color="neutral"
                size="sm"
              >
                {collapsed ? <ExpandIcon /> : <CollapseIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Box>

      <List size={collapsed ? "sm" : "lg"} sx={{ gap: 1 }}>
        {navItems.map((item) => (
          <ListItem key={item.text}>
            <Tooltip 
              title={collapsed ? item.text : ""} 
              placement="right"
              disableHoverListener={!collapsed}
            >
              <Link href={item.href} passHref style={{ width: '100%', textDecoration: 'none' }}>
                <ListItemButton 
                  selected={isActive(item.href)}
                  sx={{
                    borderRadius: 'md',
                    justifyContent: collapsed ? 'center' : 'flex-start',
                    p: collapsed ? 1 : undefined,
                    '&.Mui-selected': {
                      backgroundColor: 'primary.softBg',
                      color: 'primary.plainColor',
                    }
                  }}
                >
                  <Box sx={{ 
                    mr: collapsed ? 0 : 2, 
                    color: isActive(item.href) ? 'primary.plainColor' : 'inherit',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {item.icon}
                  </Box>
                  {!collapsed && (
                    <ListItemContent>
                      <Typography>{item.text}</Typography>
                    </ListItemContent>
                  )}
                </ListItemButton>
              </Link>
            </Tooltip>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <>
      {/* Mobile drawer */}
      <Box
        sx={{
          display: { xs: 'block', sm: 'none' },
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 1200,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          transition: 'opacity 0.3s',
          opacity: mobileOpen ? 1 : 0,
          pointerEvents: mobileOpen ? 'auto' : 'none',
        }}
        onClick={onMobileClose}
      />
      <Box
        component="nav"
        sx={{
          width: { 
            xs: 280, 
            sm: collapsed ? 70 : 280 
          },
          flexShrink: 0,
          position: 'fixed',
          top: '64px', // Adjust based on header height
          left: 0,
          height: 'calc(100vh - 114px)', // Subtract header (64px) and footer (50px) heights
          zIndex: { xs: 1300, sm: 1000 },
          backgroundColor: 'background.surface',
          borderRight: '1px solid',
          borderColor: 'divider',
          transform: { xs: mobileOpen ? 'translateX(0)' : 'translateX(-100%)', sm: 'none' },
          transition: 'all 0.3s ease-in-out',
          overflowY: 'auto',
          overflowX: 'hidden',
        }}
      >
        {sidebarContent}
      </Box>
    </>
  );
}