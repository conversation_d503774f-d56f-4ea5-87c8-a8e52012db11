'use client';

import { useState } from 'react';
import Box from '@mui/joy/Box';
import IconButton from '@mui/joy/IconButton';
import Typography from '@mui/joy/Typography';
import Menu from '@mui/joy/Menu';
import MenuItem from '@mui/joy/MenuItem';
import Container from '@mui/joy/Container';
import Link from 'next/link';
import ThemeToggle from '@/components/common/ThemeToggle';

export default function Header() {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box
      component="header"
      sx={{
        py: 2,
        borderBottom: '1px solid',
        borderColor: 'divider',
        position: 'sticky',
        top: 0,
        zIndex: 1100,
        backgroundColor: 'background.body', // Ensure the header has a background
      }}
    >
      <Container
        maxWidth="lg"
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Link href="/" passHref>
          <Typography
            component="span"
            level="h4"
            sx={{ fontWeight: 'bold', cursor: 'pointer' }}
          >
            Learneezi
          </Typography>
        </Link>

        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <Link href="/" passHref>
            <Typography component="span" sx={{ cursor: 'pointer' }}>
              Home
            </Typography>
          </Link>
          <Link href="/about" passHref>
            <Typography component="span" sx={{ cursor: 'pointer' }}>
              About
            </Typography>
          </Link>
          <Link href="/contact" passHref>
            <Typography component="span" sx={{ cursor: 'pointer' }}>
              Contact
            </Typography>
          </Link>
          <Link href="/theme-demo" passHref>
            <Typography component="span" sx={{ cursor: 'pointer' }}>
              Theme Demo
            </Typography>
          </Link>
          <ThemeToggle />
        </Box>
      </Container>
    </Box>
  );
}