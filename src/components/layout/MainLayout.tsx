'use client';

import { ReactNode, useState, useEffect } from 'react';
import Box from '@mui/joy/Box';
import Container from '@mui/joy/Container';
import IconButton from '@mui/joy/IconButton';
import Header from './Header';
import Footer from './Footer';
import Sidebar from './Sidebar';

interface MainLayoutProps {
  children: ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(280); // Default sidebar width
  
  // Update sidebar width based on window resize
  useEffect(() => {
    const handleResize = () => {
      // On small screens, sidebar is hidden or shown as overlay
      if (window.innerWidth < 600) {
        setSidebarWidth(0);
      } else {
        // Check if there's a stored preference for sidebar collapsed state
        const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        setSidebarWidth(isCollapsed ? 70 : 280);
      }
    };
    
    // Listen for sidebar collapse/expand events
    const handleSidebarToggle = (e: CustomEvent) => {
      if (window.innerWidth >= 600) { // Only for desktop
        setSidebarWidth(e.detail.collapsed ? 70 : 280);
        localStorage.setItem('sidebarCollapsed', e.detail.collapsed);
      }
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('sidebar-toggle' as any, handleSidebarToggle);
    
    // Initial setup
    handleResize();
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('sidebar-toggle' as any, handleSidebarToggle);
    };
  }, []);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        overflow: 'hidden', // Prevent scrolling on the main container
      }}
    >
      <Header />
      <Box
        sx={{
          display: 'flex',
          flex: '1 1 auto',
          overflow: 'hidden', // Prevent scrolling on this container
          position: 'relative', // For proper positioning of children
        }}
      >
        <Sidebar 
          mobileOpen={mobileOpen} 
          onMobileClose={() => setMobileOpen(false)}
        />
        
        {/* Mobile menu toggle button */}
        <Box 
          sx={{ 
            position: 'fixed', 
            bottom: 16, 
            right: 16, 
            zIndex: 1100,
            display: { xs: 'block', sm: 'none' },
            backgroundColor: 'primary.solidBg',
            borderRadius: '50%',
            boxShadow: 'md',
          }}
        >
          <IconButton 
            onClick={handleDrawerToggle}
            variant="solid"
            color="primary"
            size="lg"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </IconButton>
        </Box>
        
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            py: 4,
            width: { xs: '100%', sm: `calc(100% - ${sidebarWidth}px)` },
            ml: { xs: 0, sm: `${sidebarWidth}px` },
            transition: 'margin 0.3s ease-in-out, width 0.3s ease-in-out',
            overflow: 'auto', // Enable scrolling only for the main content area
            height: '100%', // Take full height of the parent container
          }}
        >
          <Container maxWidth="lg">{children}</Container>
        </Box>
      </Box>
      <Footer />
    </Box>
  );
}