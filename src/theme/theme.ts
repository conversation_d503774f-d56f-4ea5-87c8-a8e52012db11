import { extendTheme } from '@mui/joy/styles';

// Define your color palette
export const palette = {
  primary: {
    50: '#f0f7ff',
    100: '#c2e0ff',
    200: '#99ccf3',
    300: '#66b2ff',
    400: '#3399ff',
    500: '#007fff', // Main primary color
    600: '#0072e5',
    700: '#0059b2',
    800: '#004c99',
    900: '#003a75',
  },
  neutral: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  },
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e', // Main success color
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b', // Main warning color
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  danger: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444', // Main danger color
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
};

// Define specific colors for light and dark modes
const lightModeColors = {
  background: {
    body: '#ffffff',
    surface: '#f8fafc',
    popup: '#ffffff',
  },
  text: {
    primary: '#0f172a',
    secondary: '#475569',
    disabled: '#94a3b8',
  },
  divider: 'rgba(0, 0, 0, 0.12)',
};

const darkModeColors = {
  background: {
    body: '#0f172a',
    surface: '#1e293b',
    popup: '#1e293b',
  },
  text: {
    primary: '#f8fafc',
    secondary: '#cbd5e1',
    disabled: '#64748b',
  },
  divider: 'rgba(255, 255, 255, 0.12)',
};

// Create the theme
const theme = extendTheme({
  colorSchemes: {
    light: {
      palette: {
        primary: palette.primary,
        neutral: palette.neutral,
        success: palette.success,
        warning: palette.warning,
        danger: palette.danger,
        // Custom colors for light mode
        background: lightModeColors.background,
        text: lightModeColors.text,
        divider: lightModeColors.divider,
      },
    },
    dark: {
      palette: {
        primary: palette.primary,
        neutral: palette.neutral,
        success: palette.success,
        warning: palette.warning,
        danger: palette.danger,
        // Custom colors for dark mode
        background: darkModeColors.background,
        text: darkModeColors.text,
        divider: darkModeColors.divider,
      },
    },
  },
  fontFamily: {
    body: 'var(--font-geist-sans)',
    display: 'var(--font-geist-sans)',
    code: 'var(--font-geist-mono)',
  },
  components: {
    JoyButton: {
      styleOverrides: {
        root: ({ ownerState }) => ({
          borderRadius: '8px',
          fontWeight: 500,
        }),
      },
    },
  },
});

// Export the theme for use in the application
export default theme;