'use client';

/**
 * Utility functions for animations and parallax effects
 */

/**
 * Creates a parallax effect on an element based on scroll position
 * @param element - The DOM element to apply the parallax effect to
 * @param speed - The speed of the parallax effect (1 = normal, <1 = slower, >1 = faster)
 * @param direction - The direction of the parallax effect ('up', 'down', 'left', 'right')
 * @param maxOffset - The maximum offset in pixels
 */
export const applyParallaxEffect = (
  element: HTMLElement,
  speed: number = 0.5,
  direction: 'up' | 'down' | 'left' | 'right' = 'up',
  maxOffset: number = 100
): (() => void) => {
  if (!element) return () => {};

  // Store the original position
  const originalTransform = window.getComputedStyle(element).transform;
  const isInitiallyTransformed = originalTransform !== 'none';

  const handleScroll = () => {
    // Calculate how far the element is from the top of the viewport
    const rect = element.getBoundingClientRect();
    const elementMiddle = rect.top + rect.height / 2;
    const viewportHeight = window.innerHeight;
    const viewportMiddle = viewportHeight / 2;
    
    // Calculate the distance from the middle of the viewport as a percentage
    const distanceFromMiddle = (elementMiddle - viewportMiddle) / viewportHeight;
    
    // Calculate the offset based on the distance and speed
    let offset = distanceFromMiddle * speed * maxOffset;
    
    // Apply the offset based on the direction
    let transform = '';
    if (isInitiallyTransformed) {
      transform = originalTransform + ' ';
    }
    
    switch (direction) {
      case 'up':
        transform += `translateY(${-offset}px)`;
        break;
      case 'down':
        transform += `translateY(${offset}px)`;
        break;
      case 'left':
        transform += `translateX(${-offset}px)`;
        break;
      case 'right':
        transform += `translateX(${offset}px)`;
        break;
    }
    
    element.style.transform = transform;
  };

  // Add scroll event listener
  window.addEventListener('scroll', handleScroll);
  
  // Initial calculation
  handleScroll();
  
  // Return a cleanup function
  return () => {
    window.removeEventListener('scroll', handleScroll);
    element.style.transform = originalTransform;
  };
};

/**
 * Creates a fade-in effect on an element when it enters the viewport
 * @param element - The DOM element to apply the fade-in effect to
 * @param threshold - The threshold for when the element is considered in view (0-1)
 * @param delay - The delay in ms before starting the animation
 */
export const applyFadeInEffect = (
  element: HTMLElement,
  threshold: number = 0.2,
  delay: number = 0
): (() => void) => {
  if (!element) return () => {};

  // Store original opacity and transition
  const originalOpacity = window.getComputedStyle(element).opacity;
  const originalTransition = window.getComputedStyle(element).transition;
  
  // Set initial state
  element.style.opacity = '0';
  element.style.transition = `opacity 0.8s ease ${delay}ms`;
  
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            element.style.opacity = '1';
          }, 100); // Small delay to ensure transition works
          observer.unobserve(element);
        }
      });
    },
    { threshold }
  );
  
  observer.observe(element);
  
  // Return cleanup function
  return () => {
    observer.unobserve(element);
    element.style.opacity = originalOpacity;
    element.style.transition = originalTransition;
  };
};

/**
 * Hook to apply parallax effect to multiple elements
 * @param selector - CSS selector for the elements to apply parallax to
 * @param options - Configuration options for the parallax effect
 */
export const initParallaxElements = (
  selector: string,
  options: {
    speed?: number;
    direction?: 'up' | 'down' | 'left' | 'right';
    maxOffset?: number;
    staggerDelay?: number;
  } = {}
): (() => void) => {
  const {
    speed = 0.5,
    direction = 'up',
    maxOffset = 100,
    staggerDelay = 0
  } = options;
  
  const elements = document.querySelectorAll<HTMLElement>(selector);
  const cleanupFunctions: Array<() => void> = [];
  
  elements.forEach((element, index) => {
    const delay = index * staggerDelay;
    setTimeout(() => {
      const cleanup = applyParallaxEffect(element, speed, direction, maxOffset);
      cleanupFunctions.push(cleanup);
    }, delay);
  });
  
  return () => {
    cleanupFunctions.forEach(cleanup => cleanup());
  };
};