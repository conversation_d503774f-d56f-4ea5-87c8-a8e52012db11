/**
 * Colors utility file for accessing theme colors throughout the application
 */

import { useColorScheme } from '@mui/joy/styles';
import { palette } from '@/theme/theme';

// Type definitions for color utilities
type ColorShade = 50 | 100 | 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900;
type ColorType = 'primary' | 'neutral' | 'success' | 'warning' | 'danger';
type BackgroundType = 'body' | 'surface' | 'popup';
type TextType = 'primary' | 'secondary' | 'disabled';

/**
 * Get a specific color from the palette
 * @param colorType The color type (primary, neutral, etc.)
 * @param shade The color shade (50-900)
 * @returns The color hex value
 */
export const getColor = (colorType: ColorType, shade: ColorShade): string => {
  return palette[colorType][shade];
};

/**
 * Hook to get theme-aware colors based on current color mode
 */
export const useThemeColors = () => {
  const { mode } = useColorScheme();
  const isDark = mode === 'dark';

  return {
    // Get background colors
    background: {
      body: isDark ? '#0f172a' : '#ffffff',
      surface: isDark ? '#1e293b' : '#f8fafc',
      popup: isDark ? '#1e293b' : '#ffffff',
      get: (type: BackgroundType) => isDark ? 
        { body: '#0f172a', surface: '#1e293b', popup: '#1e293b' }[type] : 
        { body: '#ffffff', surface: '#f8fafc', popup: '#ffffff' }[type],
    },
    
    // Get text colors
    text: {
      primary: isDark ? '#f8fafc' : '#0f172a',
      secondary: isDark ? '#cbd5e1' : '#475569',
      disabled: isDark ? '#64748b' : '#94a3b8',
      get: (type: TextType) => isDark ? 
        { primary: '#f8fafc', secondary: '#cbd5e1', disabled: '#64748b' }[type] : 
        { primary: '#0f172a', secondary: '#475569', disabled: '#94a3b8' }[type],
    },
    
    // Get divider color
    divider: isDark ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
    
    // Get a color with specific shade
    getColor,
    
    // Get main colors (shade 500)
    primary: palette.primary[500],
    neutral: palette.neutral[500],
    success: palette.success[500],
    warning: palette.warning[500],
    danger: palette.danger[500],
    
    // Check current mode
    isDark,
    isLight: !isDark,
  };
};